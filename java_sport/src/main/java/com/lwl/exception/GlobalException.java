package com.lwl.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.lwl.model.Result;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class GlobalException {
    @ExceptionHandler(value = NotLoginException.class)
    public Result<String> handle(NotLoginException e, HttpServletRequest request) {
        // 如果没有传递token时, 应返回未传递登陆凭证
        String tokenName = request.getHeader(StpUtil.getTokenName());
        if (StrUtil.isBlank(tokenName))
            return Result.error(401, "未传递登陆凭证", request.getRequestURI());
        log.error("Sa-Token鉴权异常 --- url: {}", request.getRequestURI(), e);
        return Result.error(401, e.getMessage(), request.getRequestURI());
    }

    @ExceptionHandler(value = NotPermissionException.class)
    public Result<String> handle(NotPermissionException e, HttpServletRequest request) {
        log.error("Sa-Token鉴权异常 --- url: {}", request.getRequestURI(), e);
        //权限不足
        return Result.error(403, e.getMessage(), request.getRequestURI());
    }

    /**
     * 无角色异常
     * @param e 异常
     * @param request 请求
     * @return 异常信息
     */
    @ExceptionHandler(value = NotRoleException.class)
    public Result<String> handle(NotRoleException e, HttpServletRequest request) {
        log.error("Sa-Token鉴权异常 --- url: {}", request.getRequestURI(), e);
        return Result.error(403, e.getMessage(), request.getRequestURI());
    }
}
